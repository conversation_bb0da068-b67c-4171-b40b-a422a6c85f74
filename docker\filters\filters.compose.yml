x-filter-service: &filter-service
  build:
    context: ../../
    dockerfile: docker/filters/filter.Dockerfile
  depends_on:
    rabbitmq:
      condition: service_healthy
  networks:
    - app-network
  env_file: ../../.env
  volumes:
    - ../../config/filter_config.yaml:/filter_config.yaml
   
services:
  filter-by-year-0:
    <<: *filter-service
    container_name: filter-by-year-0
    environment:
      - FILTER_TYPE=year
      - CLUSTER_SIZE=1
      - CONFIG_PATH=/filter_config.yaml

  filter-by-time-0:
    <<: *filter-service
    container_name: filter-by-time-0
    environment:
      - FILTER_TYPE=time
      - CLUSTER_SIZE=1
      - CONFIG_PATH=/filter_config.yaml
    depends_on:
- filter-by-year-0

  filter-by-amount-0:
    <<: *filter-service
    container_name: filter-by-amount-0
    environment:
      - FILTER_TYPE=amount
      - CLUSTER_SIZE=1
      - CONFIG_PATH=/filter_config.yaml
    depends_on:
- filter-by-time-0

