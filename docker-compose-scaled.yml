name: coffee_shop_analysis_scaled

include:
  - path:
    - docker/filters/filters.compose.yml
    - docker/groupers/groupers.compose.yml
    - docker/aggregators/aggregators.compose.yml
    - docker/joiners/joiners.compose.yml

services:
  # RABBIT MQ
  rabbitmq:
    image: rabbitmq:4-management
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - app-network
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    healthcheck:
      test: ["CMD-SHELL", "rabbitmq-diagnostics check_port_connectivity"]
      interval: 10s
      timeout: 5s
      retries: 10

  gateway-0:
    container_name: gateway-0
    build:
      context: .
      dockerfile: docker/server.Dockerfile
    image: server:latest
    env_file: ./.env
    networks:
      - app-network
    depends_on:
      rabbitmq:
        condition: service_healthy

  preprocessor-0:
    container_name: preprocessor-0
    build:
      context: .
      dockerfile: docker/preprocessor.Dockerfile
    image: preprocessor:latest
    env_file: ./.env
    environment:
      - CONFIG_PATH=/preprocessor_config.yaml
      - CLUSTER_SIZE=1
    depends_on:
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    volumes:
      - ./config/preprocessor_config.yaml:/preprocessor_config.yaml

networks:
  app-network:
    driver: bridge
    name: alpesto-g20
