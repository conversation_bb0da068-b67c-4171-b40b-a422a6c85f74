x-aggregator-service: &aggregator-service
  build:
    context: ../../
    dockerfile: docker/aggregators/aggregator.Dockerfile
  depends_on:
    rabbitmq:
      condition: service_healthy
  networks:
    - app-network
  env_file: ../../.env

services:
  aggregate-top-seller-product-0:
    <<: *aggregator-service
    container_name: aggregate-top-seller-product-0
    environment:
      - AGGREGATOR_TYPE=top_seller_product

  aggregate-top-profit-product-0:
    <<: *aggregator-service
    container_name: aggregate-top-profit-product-0
    environment:
      - AGGREGATOR_TYPE=top_profit_product

  aggregate-top-client-0:
    <<: *aggregator-service
    container_name: aggregate-top-client-0
    environment:
      - AGGREGATOR_TYPE=client

  aggregate-tpv-0:
    <<: *aggregator-service
    container_name: aggregate-tpv-0
    environment:
      - AGGREGATOR_TYPE=tpv

