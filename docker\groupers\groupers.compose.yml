x-grouper-service: &grouper-service
  build:
    context: ../../
    dockerfile: docker/groupers/group_by.Dockerfile
  depends_on:
    rabbitmq:
      condition: service_healthy
  networks:
    - app-network
  env_file: ../../.env

services:
  group-by-year-month-product-0:
    <<: *grouper-service
    container_name: group-by-year-month-product-0
    environment:
      - GROUP_TYPE=year-month-product

  group-by-year-month-user-store-0:
    <<: *grouper-service
    container_name: group-by-year-month-user-store-0
    environment:
      - GROUP_TYPE=year-month-user-store

  group-by-year-semester-store-0:
    <<: *grouper-service
    container_name: group-by-year-semester-store-0
    environment:
      - GROUP_TYPE=year-semester-store

